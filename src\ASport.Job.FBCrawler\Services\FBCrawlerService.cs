using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using Koo.DependencyInjection;
using ASport.Job.FBCrawler.Models;
using ASport.Job.FBCrawler.Options;
using System.Diagnostics;
using System.Collections.Concurrent;

namespace ASport.Job.FBCrawler.Services
{
    public class FBCrawlerService : ISingleton, IDisposable {
        private readonly string _baseUrl;
        private const string MatchListUrl = "/v1/match/getList";
        private const string ResultListUrl = "/v1/match/matchResultList";

        private readonly List<string> _proxies;
        private readonly ConcurrentBag<string> _failedProxies;
        private int _currentProxyIndex = 0;
        private readonly object _proxyLock = new object();
        
        // HttpClient 池，每个代理对应一个 HttpClient 实例
        private readonly Dictionary<string, HttpClient> _httpClientPool = new Dictionary<string, HttpClient>();
        private readonly HttpClient _defaultHttpClient; // 不使用代理的默认 HttpClient

        private readonly ILogger<FBCrawlerService> _logger;
        public FBCrawlerService(JobOptions jobOptions, ILogger<FBCrawlerService> logger) {
            _logger = logger;
            _baseUrl = jobOptions.fbBaseUrl;
            _proxies = jobOptions.proxies ?? new List<string>();
            _failedProxies = new ConcurrentBag<string>();
            
            // 创建默认的 HttpClient（不使用代理）
            var defaultHandler = new SocketsHttpHandler {
                ConnectTimeout = TimeSpan.FromSeconds(10),
                MaxConnectionsPerServer = 100, // Optimize connection pooling
                PooledConnectionIdleTimeout = TimeSpan.FromMinutes(2), // Keep connections alive for 2 minutes
                PooledConnectionLifetime = TimeSpan.FromMinutes(5) // Max lifetime for pooled connections
            };
            _defaultHttpClient = new HttpClient(defaultHandler) {
                Timeout = TimeSpan.FromSeconds(10),
                BaseAddress = new Uri(_baseUrl)
            };
            
            
            ConfigureHttpClient(_defaultHttpClient);
        }

        private void ConfigureHttpClient(HttpClient httpClient) {
            httpClient.BaseAddress = new Uri(_baseUrl);
            httpClient.DefaultRequestHeaders.Clear();

            // 设置请求头
            httpClient.DefaultRequestHeaders.Accept.ParseAdd("application/json, text/plain, */*");
            httpClient.DefaultRequestHeaders.AcceptLanguage.ParseAdd("zh-CN,zh;q=0.9");
            httpClient.DefaultRequestHeaders.Connection.ParseAdd("keep-alive");
            httpClient.DefaultRequestHeaders.Host = "sportapi.fastball2.com";
            httpClient.DefaultRequestHeaders.Referrer = new Uri("https://test.f66b88sport.com/");
            httpClient.DefaultRequestHeaders.Add("Origin", "https://test.f66b88sport.com");
            httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Dest", "empty");
            httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Mode", "cors");
            httpClient.DefaultRequestHeaders.Add("Sec-Fetch-Site", "cross-site");
            httpClient.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            httpClient.DefaultRequestHeaders.Add("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"");
            httpClient.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
            httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");
        }

        private string GetNextProxy() {
            if (_proxies == null || _proxies.Count == 0) {
                return null;
            }

            lock (_proxyLock) {
                // 过滤掉失败的代理
                var availableProxies = _proxies.Where(p => !_failedProxies.Contains(p)).ToList();
                
                // 如果没有可用的代理，清空失败列表并重试
                if (availableProxies.Count == 0) {
                    _logger.LogWarning("所有代理都已失败，清空失败列表重试");
                    _failedProxies.Clear();
                    availableProxies = _proxies.ToList();
                }

                if (availableProxies.Count == 0) {
                    return null;
                }

                // 轮询获取下一个代理
                var proxy = availableProxies[_currentProxyIndex % availableProxies.Count];
                _currentProxyIndex = (_currentProxyIndex + 1) % availableProxies.Count;
                _logger.LogInformation("当前代理: {proxy}", proxy);
                return proxy;
            }
        }

        private HttpClient GetOrCreateHttpClientWithProxy(string proxyUrl) {
            // 如果没有代理，返回默认的 HttpClient
            if (string.IsNullOrEmpty(proxyUrl)) {
                return _defaultHttpClient;
            }

            lock (_proxyLock) {
                // 如果已经为该代理创建了 HttpClient，则复用
                if (_httpClientPool.TryGetValue(proxyUrl, out var cachedClient)) {
                    return cachedClient;
                }

                try {
                    var handler = new SocketsHttpHandler {
                        ConnectTimeout = TimeSpan.FromSeconds(10),
                        MaxConnectionsPerServer = 100,
                        PooledConnectionIdleTimeout = TimeSpan.FromMinutes(2),
                        PooledConnectionLifetime = TimeSpan.FromMinutes(5),
                        Proxy = new System.Net.WebProxy(proxyUrl),
                        UseProxy = true
                    };

                    var client = new HttpClient(handler) {
                        Timeout = TimeSpan.FromSeconds(10),
                        BaseAddress = new Uri(_baseUrl)
                    };

                    // 配置请求头
                    ConfigureHttpClient(client);

                    // 将新创建的 HttpClient 添加到池中
                    _httpClientPool[proxyUrl] = client;
                    
                    _logger.LogDebug("为代理 {proxy} 创建了新的 HttpClient 实例", proxyUrl);
                    return client;
                }
                catch (Exception ex) {
                    _logger.LogError(ex, "为代理 {proxy} 创建 HttpClient 失败", proxyUrl);
                    MarkProxyAsFailed(proxyUrl);
                    // 如果创建失败，返回默认的 HttpClient
                    return _defaultHttpClient;
                }
            }
        }

        private void MarkProxyAsFailed(string proxyUrl) {
            if (!string.IsNullOrEmpty(proxyUrl)) {
                _failedProxies.Add(proxyUrl);
                _logger.LogWarning("标记代理为失败: {proxy}", proxyUrl);
            }
        }

        private async Task<HttpResponseMessage> SendRequestWithProxyAsync(HttpRequestMessage request) {
            var proxy = GetNextProxy();
            
            // 获取或创建对应代理的 HttpClient
            var client = GetOrCreateHttpClientWithProxy(proxy);

            try {
                var response = await client.SendAsync(request);
                return response;
            }
            catch (HttpRequestException ex) {
                if (!string.IsNullOrEmpty(proxy)) {
                    _logger.LogError(ex, "代理请求失败: {proxy}", proxy);
                    MarkProxyAsFailed(proxy);
                    // 递归尝试下一个代理
                    return await SendRequestWithProxyAsync(request);
                }
                throw;
            }
        }

        public async Task<List<MatchRecord>> GetMatchListAsync(string languageType = "CMN", List<int> leagueIds = null) {
            var records = new List<MatchRecord>();

            var current = 1;
            while (current <= 10) {
                var (data, getMore) = await _GetMatchListAsync(current: current, languageType: languageType, leagueIds: leagueIds);
                records.AddRange(data);
                if (getMore) {
                    current++;
                } else {
                    break;
                }
            }
            
            // 对结果进行去重处理，确保没有重复的Id
            var distinctRecords = records.GroupBy(r => r.Id).Select(g => g.First()).ToList();
            return distinctRecords;
        }

        private async Task<(List<MatchRecord>,bool)> _GetMatchListAsync(int current = 1, string languageType = "CMN", List<int> leagueIds = null) {
            var sw = new Stopwatch();
            sw.Start();
            int retryCount = 0;
            const int maxRetries = 3;
            
            while (retryCount < maxRetries) {
                try {
                    // 默认请求数据
                    var data = new {
                        languageType = languageType,
                        current = current,
                        orderBy = 1,
                        isPC = true,
                        sportId = 1,
                        type = 1,
                        leagueIds = leagueIds
                    };

                    var json = JsonSerializer.Serialize(data);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // 添加Content-Type头
                    content.Headers.ContentType = new MediaTypeHeaderValue("application/json") {
                        CharSet = "UTF-8"
                    };

                    var request = new HttpRequestMessage(HttpMethod.Post, MatchListUrl) {
                        Content = content
                    };
                    
                    var response = await SendRequestWithProxyAsync(request);
                    response.EnsureSuccessStatusCode();

                    var responseContent = await response.Content.ReadAsStringAsync();

                    // Deserialize the response content into the model
                    var apiResponse = JsonSerializer.Deserialize<MatchApiResponse<MatchListData>>(responseContent, new JsonSerializerOptions {
                        PropertyNameCaseInsensitive = true
                    });
                    sw.Stop();
                    _logger.LogInformation("[MatchList]请求第{page}页用时: {ms} ms", current, sw.ElapsedMilliseconds);
                    if (apiResponse is { Success: true, Data.Records: not null }) {
                        if (apiResponse.Data.Size == apiResponse.Data.Records.Count) {
                            return (apiResponse.Data.Records, true);
                        } else {
                            return (apiResponse.Data.Records, false);
                        }
                    } else {

                        if (apiResponse.Code == 8) {
                            _logger.LogWarning("fb维护中...");
                            await Task.Delay(TimeSpan.FromSeconds(60));
                        } else {
                            _logger.LogError("[MatchList]获取失败：{content}", responseContent);
                        }
                        return ([], false);
                    }
                    
                } catch (HttpRequestException ex) when (retryCount < maxRetries - 1) {
                    retryCount++;
                    _logger.LogWarning("[MatchList]请求第{page}页失败，第{retryCount}次重试: {msg}", current, retryCount, ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(2 * retryCount)); // 指数退避
                } catch (TaskCanceledException ex) when (retryCount < maxRetries - 1) {
                    retryCount++;
                    _logger.LogWarning("[MatchList]请求第{page}页超时，第{retryCount}次重试: {msg}", current, retryCount, ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(2 * retryCount)); // 指数退避
                } catch (Exception ex) when (retryCount < maxRetries - 1) {
                    retryCount++;
                    _logger.LogWarning("[MatchList]获取第{page}页比赛列表时发生错误，第{retryCount}次重试: {msg}", current, retryCount, ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(2 * retryCount)); // 指数退避
                } catch (Exception ex) {
                    _logger.LogError("[MatchList]获取第{page}页比赛列表时发生错误，已达到最大重试次数: {msg}", current, ex.Message);
                    return ([], false);
                }
            }
            return ([], false);
        }




        public async Task<List<MatchResultData>> GetResultListAsync(long beginTime, long endTime, string languageType = "CMN") {
            var sw = new Stopwatch();
            sw.Start();
            int retryCount = 0;
            const int maxRetries = 3;
            
            while (retryCount < maxRetries) {
                try {
                    // 默认请求数据
                    var data = new {
                        languageType = languageType,
                        sportId = 1,
                        beginTime = beginTime,
                        endTime = endTime
                    };

                    var json = JsonSerializer.Serialize(data);
                    var content = new StringContent(json, Encoding.UTF8, "application/json");

                    // 添加Content-Type头
                    content.Headers.ContentType = new MediaTypeHeaderValue("application/json") {
                        CharSet = "UTF-8"
                    };

                    var request = new HttpRequestMessage(HttpMethod.Post, ResultListUrl) {
                        Content = content
                    };
                    
                    var response = await SendRequestWithProxyAsync(request);
                    response.EnsureSuccessStatusCode();

                    var responseContent = await response.Content.ReadAsStringAsync();

                    // Deserialize the response content into the model
                    var apiResponse = JsonSerializer.Deserialize<MatchApiResponse<List<MatchResultData>>>(responseContent, new JsonSerializerOptions {
                        PropertyNameCaseInsensitive = true
                    });
                    sw.Stop();
                    _logger.LogInformation("[ResultList]请求用时: {ms} ms", sw.ElapsedMilliseconds);
                    if (apiResponse is { Success: true, Data: not null }) {
                        return apiResponse.Data;
                    } else {

                        if (apiResponse.Code == 8) {
                            _logger.LogWarning("fb维护中...");
                            await Task.Delay(TimeSpan.FromSeconds(60));
                        } else {
                            _logger.LogError("[MatchList]获取失败：{content}", responseContent);
                        }
                        return [];
                    }
                } catch (HttpRequestException ex) when (retryCount < maxRetries - 1) {
                    retryCount++;
                    _logger.LogWarning("[ResultList]请求失败，第{retryCount}次重试: {msg}", retryCount, ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(2 * retryCount)); // 指数退避
                } catch (TaskCanceledException ex) when (retryCount < maxRetries - 1) {
                    retryCount++;
                    _logger.LogWarning("[ResultList]请求超时，第{retryCount}次重试: {msg}", retryCount, ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(2 * retryCount)); // 指数退避
                } catch (Exception ex) when (retryCount < maxRetries - 1) {
                    retryCount++;
                    _logger.LogWarning("[ResultList]获取比赛列表时发生错误，第{retryCount}次重试: {msg}", retryCount, ex.Message);
                    await Task.Delay(TimeSpan.FromSeconds(2 * retryCount)); // 指数退避
                } catch (Exception ex) {
                    _logger.LogError("[ResultList]获取比赛列表时发生错误，已达到最大重试次数: {msg}", ex.Message);
                    return [];
                }
            }
            return [];
        }

       
        public void Dispose() {
            // 释放所有 HttpClient 资源
            foreach (var client in _httpClientPool.Values) {
                client?.Dispose();
            }
            _httpClientPool.Clear();
            
            _defaultHttpClient?.Dispose();
        }
    }
}
