## 比分项目(`nsg.tyg`)
- 5：比分
- 6：角球
- 7：黄牌
- 8：红牌

## 阶段（`nsg.pe`）
- 1000：实时
- 1001：全场
- 1002：上半场
- 1003：下半场
- 1004：加时上半场
- 1005：加时下半场
- 1006：点球
- 1007：比赛开始-14:59分钟
- 1008：15:00-29:59分钟
- 1009：30:00-中场休息
- 1010：下半场开始-59:59分钟
- 1011：60:00-74:59分钟
- 1012：75:00-90分钟全场结束
- 1013：加时全场
- 1014：全场（包含加时）
- 1015：点球大战前5轮

## 玩法（`mg`）

| 玩法     | 条件                          |
| ------ | --------------------------- |
| 让球     | `mty == 1000 && pe == 1001` |
| 让球-上半场 | `mty == 1000 && pe == 1002` |
| 让球-下半场 | `mty == 1000 && pe == 1003` |
| 大小     | `mty == 1007 && pe == 1001` |
| 大小-上半场 | `mty == 1007 && pe == 1002` |
| 大小-下半场 | `mty == 1007 && pe == 1003` |
| 独赢     | `mty == 1005 && pe == 1001` |
| 独赢-上半场 | `mty == 1005 && pe == 1002` |
| 独赢-下半场 | `mty == 1005 && pe == 1003` |
| 单双     | `mty == 1008 && pe == 1001` |

---

## 赛事进程

* **时间**：`mc.s` 秒 → `mm:ss` 格式
* **阶段**：`mc.pe`

  * `1001`：未开赛
  * `1002`：上半场
  * `1003`：中场休息
  * `1004`：下半场
  * `1005`：常规时间结束
  * `1006`：加时上半场
  * `1007`：加时中场休息
  * `1008`：加时下半场
  * `1009`：加时结束
  * `1010`：点球大战
  * `1011`：完场

