using Koo.Core.Options;

namespace ASport.Job.FBCrawler.Options
{
    public class JobOptions : ISingletonOptions {
        public int crawlerInterval { get; set; }

        public int resultInterval { get; set; }

        public List<int> leagueIds { get; set; } = [];

        public string hotKey { get; set; } 
        public string otherKey { get; set; }

        public string fbBaseUrl { get; set; }

        public List<string> proxies { get; set; } = [];

        public bool resetMatchesOnStart { get; set; }
    }
}
