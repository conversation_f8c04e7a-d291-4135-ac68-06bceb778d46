using System.Text.Encodings.Web;
using System.Text.Json;
using Koo.Dapper.Extensions;

namespace ASport.Job.FBCrawler.Models
{
    [Table("fb_matches")]
    public class FbMatch
    {
        private static readonly JsonSerializerOptions options = new() {
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            WriteIndented = false
        };

        public int mid { get; set; }

        public int gomid { get; set; }

   
        public byte Sid { get; set; }

        [Write(false)]
        public Dictionary<string, string> LnameDict { get; set; } = [];

        [Write(false)]
        public Dictionary<string, string> AnameDict { get; set; } = [];

        [Write(false)]
        public Dictionary<string, string> BnameDict { get; set; } = [];


        public string lname => JsonSerializer.Serialize(LnameDict, options);


        public string aname => JsonSerializer.Serialize(AnameDict, options);
        

        public string bname => JsonSerializer.Serialize(BnameDict, options);

      
        public string llogo { get; set; }

        
        public string alogo { get; set; }


        public string blogo { get; set; }


        public DateTime mtime { get; set; }

        public int golid { get; set; }
        
        public int goaid { get; set; }
        
        public int gobid { get; set; }

        [Write(false)]
        public SportProcess sportProcess { get; set; } = new();

        [Write(false)]
        public List<BetItem> betItems { get; set; } = [];
    }
}
