using Koo.Core.Options;

namespace ASport.Job.FBCrawler.Options {
  public class GoMatchOptions : ISingletonOptions {
    public Dictionary<string, int> ProcessTypeMapping { get; set; } = [];
    public Dictionary<string, BetItemConfig> BetItemMapping { get; set; } = [];

    public Dictionary<string, byte> LangMapping { get; set; } = [];

    public Dictionary<string, ResultConfig> ResultMapping { get; set; } = [];
  }

  public class BetItemConfig {
    public int Gid { get; set; }
    public int Smode { get; set; }
    public string Name { get; set; }
    public byte Hot { get; set; }
  }

  public class ResultConfig {
    public int rid { get; set; }
    public string rcode { get; set; }

    public byte type { get; set; }
  }
}