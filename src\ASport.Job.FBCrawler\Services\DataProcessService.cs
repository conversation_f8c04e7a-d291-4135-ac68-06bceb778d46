using ASport.Job.FBCrawler.Models;
using Koo.DependencyInjection;
using ASport.Job.FBCrawler.Options;
using System.Text.Json;
using Koo.Extensions.Common;
using ASport.Job.FBCrawler.Models.GoSport;
using System.Diagnostics;

namespace ASport.Job.FBCrawler.Services;

public class DataProcessService : ISingleton
{
    private readonly DBService _dbService;
    private readonly FBCrawlerService _fbCrawlerService;
    private readonly ILogger<DataProcessService> _logger;
    private readonly GoMatchOptions _goMatchOptions;
    private readonly JobOptions _jobOptions;

    private Dictionary<int, FbMatch> _fbMatches = [];
    private Dictionary<string, BetItem> _betItemsCache = [];
    private readonly HashSet<int> _pendingMids = [];
    private bool _isFirstRun = true;
    
    // 缓存最近出现的 gomid，用于检查漏掉的赛事
    private readonly HashSet<int> _recentGoMids = new HashSet<int>();
    private int _processCount = 0; // 处理计数器
    private const int CheckInterval = 50; // 每50次处理检查一次

    public DataProcessService(DBService dbService, FBCrawlerService fbCrawlerService, ILogger<DataProcessService> logger, GoMatchOptions goMatchOptions, JobOptions jobOptions)
    {
        _dbService = dbService;
        _fbCrawlerService = fbCrawlerService;
        _logger = logger;
        _goMatchOptions = goMatchOptions;
        _jobOptions = jobOptions;
    }

    public async Task<List<FbMatch>> ProcessRawResponse(List<MatchRecord> records)
    {

        var rawRecordsByMid = records.ToDictionary(r => (int)r.Id);

        // 1. 将比赛缓存与API中当前活跃的比赛列表同步
        await SyncMatchesCache(rawRecordsByMid);

        // 2. 使用同步后的缓存和新数据构建新的、完全填充的比赛列表
        var newMatchList = BuildNewMatchList(rawRecordsByMid);

        // Perform cleanup of active GoMatches that are not in the current crawl on first run
        if (_jobOptions.resetMatchesOnStart && _isFirstRun)
        {
            // Load hot bet items from Redis
            var hotBetItems = await _dbService.GetRedisHashAll<BetItem>(_jobOptions.hotKey);
            foreach (var item in hotBetItems.Values.Where(bi => bi.state == 1))
            {
                _betItemsCache[item.id.ToString()] = item;
            }

            // Load other bet items from Redis
            var otherBetItems = await _dbService.GetRedisHashAll<BetItem>(_jobOptions.otherKey);
            foreach (var item in otherBetItems.Values.Where(bi => bi.state == 1))
            {
                _betItemsCache[item.id.ToString()] = item;
            }

            var activeGoMids = await _dbService.GetActiveGoMatchesForCleanup();
            var currentFbMatchGoMids = newMatchList.Where(m => m.gomid > 0).Select(m => m.gomid).ToHashSet();
            var goMidsToCleanup = activeGoMids.Except(currentFbMatchGoMids).ToList();

            if (goMidsToCleanup.Count != 0)
            {
                _logger.LogInformation("首次运行：发现 {Count} 场需要清理的活跃 Go 赛事。", goMidsToCleanup.Count);
                await _dbService.CloseGoMatches(goMidsToCleanup);
                
                // 从缓存中移除这些已关闭的 gomid
                foreach (var gomid in goMidsToCleanup)
                {
                    RemoveGoMidFromCache(gomid);
                }
            }
            _isFirstRun = false;
        }

        // 3. 将新状态与旧状态（_fbMatches）进行比较并持久化更改
        var persistSportProcessChangesTask = PersistSportProcessChanges(_fbMatches, newMatchList);
        var persistMatchGCountsTask = PersistMatchGCounts(newMatchList);
        var updateProcessTime = await persistSportProcessChangesTask;
        var (noUpdateGcountMids, updateMatchTime) = await persistMatchGCountsTask;
        

        var updateBetItemsTime = await PersistBetItemChanges(newMatchList, noUpdateGcountMids);

        // 4. 更新内存缓存
        UpdateLocalCache(newMatchList);

        // 5. 增加处理计数器，每50次检查一次漏掉的赛事
        _processCount++;
        if (_processCount >= CheckInterval)
        {
            _processCount = 0;
            await CheckMissingMatches();
        }

        _logger.LogInformation("成功处理了 {Count} 场比赛。更新时间: Process: {updateProcessTime} ms, Match: {updateMatchTime} ms, BetItems: {updateBetItemsTime} ms", newMatchList.Count, updateProcessTime, updateMatchTime, updateBetItemsTime);
        return newMatchList;
    }

    private void UpdateLocalCache(List<FbMatch> newMatchList)
    {
        _fbMatches = newMatchList.ToDictionary(x => x.mid);
        _betItemsCache = newMatchList.SelectMany(x => x.betItems).ToDictionary(x => x.id.ToString());
        
        // 将新的 gomid 添加到缓存中
        foreach (var match in newMatchList)
        {
            if (match.gomid > 0)
            {
                AddGoMidToCache(match.gomid);
            }
        }
    }

    private async Task<(List<int>, long)> PersistMatchGCounts(List<FbMatch> newMatchList)
    {
        var data = new List<int>();
        var sw = new Stopwatch();
        sw.Start();
        var updateDatas = newMatchList
            .Where(m => m.gomid > 0 && m.betItems.Count > 0)
            .Select(m => new UpdateSportMatchData {
              mid = m.gomid,
              gcount = m.betItems.Count,
              mtime = m.mtime
            })
            .ToList();

        if (updateDatas.Count != 0)
        {
            data = await _dbService.BulkUpdateSportMatch(updateDatas);
        }
        sw.Stop();

        return (data,sw.ElapsedMilliseconds);
    }

    private async Task<long> PersistBetItemChanges(List<FbMatch> newMatchList, List<int> noUpdateGcountMids) {
        var sw = new Stopwatch();
        sw.Start();
        var redisHotUpdates = new Dictionary<string, object>();
        var redisOtherUpdates = new Dictionary<string, object>();

        // 从新列表中获取所有投注项以便快速查找
        var newBetItems = newMatchList
            .Where(x => !noUpdateGcountMids.Contains(x.mid))
            .SelectMany(m => m.betItems)
            .ToDictionary(bi => bi.id.ToString());

        // 1. 从 _betItemsCache 中查找不在新列表中的过时投注项,已结束的赛事id全部设置state=2
        foreach (var cachedBetItem in _betItemsCache.Values.Where(x => !noUpdateGcountMids.Contains(x.mid))) {
            var cachedBetItemId = cachedBetItem.id.ToString();
            if (!newBetItems.ContainsKey(cachedBetItemId)) {
                // 此投注项已过时，标记为已关闭
                cachedBetItem.state = 2;
                if (cachedBetItem.hot == 1) {
                    redisHotUpdates[cachedBetItemId] = cachedBetItem;
                } else {
                    redisOtherUpdates[cachedBetItemId] = cachedBetItem;
                }
            }
        }

        // 2. 添加所有新的/更新的投注项。这将覆盖任何被重新激活的过时项。只更新cupdate=1的赛事
        foreach (var newBetItem in newBetItems.Values.Where(x => !noUpdateGcountMids.Contains(x.mid))) {
            var newBetItemId = newBetItem.id.ToString();
            if (newBetItem.hot == 1) {
                redisHotUpdates[newBetItemId] = newBetItem;
            } else {
                redisOtherUpdates[newBetItemId] = newBetItem;
            }
        }

        // 3. 批量更新 Redis
        if (redisHotUpdates.Count != 0) {
            await _dbService.BulkUpdateRedis(_jobOptions.hotKey, redisHotUpdates);
        }
        if (redisOtherUpdates.Count != 0) {
            await _dbService.BulkUpdateRedis(_jobOptions.otherKey, redisOtherUpdates);
        }
        sw.Stop();
        return sw.ElapsedMilliseconds;
    }

    private async Task<long> PersistSportProcessChanges(Dictionary<int, FbMatch> oldMatches, List<FbMatch> newMatchList) {
        var sw = new Stopwatch();
        sw.Start();
        var processesToUpdate = new List<SportProcess>();

        foreach (var newMatch in newMatchList) {
            if (newMatch.gomid == 0) continue;

            // 尝试从缓存中查找旧的比赛数据
            if (oldMatches.TryGetValue(newMatch.mid, out var oldMatch)) {
                // 比较 SportProcess 对象以查看是否有更改
                var changed = false;
                if (!changed && newMatch.sportProcess.type != oldMatch.sportProcess.type) changed = true;
                if (!changed && newMatch.sportProcess.time != oldMatch.sportProcess.time) changed = true;
                if (!changed && newMatch.sportProcess.ascore != oldMatch.sportProcess.ascore) changed = true;
                if (!changed && newMatch.sportProcess.bscore != oldMatch.sportProcess.bscore) changed = true;
                if (!changed && newMatch.sportProcess.ared != oldMatch.sportProcess.ared) changed = true;
                if (!changed && newMatch.sportProcess.bred != oldMatch.sportProcess.bred) changed = true;
                if (!changed && newMatch.sportProcess.extra != oldMatch.sportProcess.extra) changed = true;
                if (!changed && newMatch.sportProcess.scores != oldMatch.sportProcess.scores) changed = true;

                if (changed) {
                    processesToUpdate.Add(newMatch.sportProcess);
                }
            } else {
                // 如果它不在旧缓存中，则需要创建新的比赛进程。
                processesToUpdate.Add(newMatch.sportProcess);
            }
        }

        if (processesToUpdate.Count != 0) {
            await _dbService.BulkUpsertSportProcess(processesToUpdate);
        }
        sw.Stop();
        return sw.ElapsedMilliseconds;
    }

    private List<FbMatch> BuildNewMatchList(Dictionary<int, MatchRecord> rawRecordsByMid)
    {
        var newMatches = new List<FbMatch>();

        // 遍历同步的缓存（_fbMatches），其中包含要处理的比赛的最终列表
        _fbMatches.Values.AsParallel().ForAll(cachedMatch => {
            // 跳过正在异步处理的赛事
            if (_pendingMids.Contains(cachedMatch.mid)) {
                _logger.LogDebug("跳过正在异步处理的赛事 {FbMid}", cachedMatch.mid);
            }

            // 从最新的API调用中查找相应的原始数据
            if (rawRecordsByMid.TryGetValue(cachedMatch.mid, out var record)) {
                // 创建一个新的FbMatch对象来保存完全填充的数据
                var newMatch = new FbMatch {
                    mid = cachedMatch.mid,
                    gomid = cachedMatch.gomid,
                    golid = cachedMatch.golid,
                    goaid = cachedMatch.goaid,
                    gobid = cachedMatch.gobid,
                    Sid = 1,
                    LnameDict = record.Lg.Na != null ? new Dictionary<string, string> { { "CMN", record.Lg.Na } } : [],
                    AnameDict = record.Ts[0].Na != null ? new Dictionary<string, string> { { "CMN", record.Ts[0].Na } } : [],
                    BnameDict = record.Ts[1].Na != null ? new Dictionary<string, string> { { "CMN", record.Ts[1].Na } } : [],
                    llogo = record.Lg.Lurl,
                    alogo = record.Ts[0].Lurl,
                    blogo = record.Ts[1].Lurl,
                    mtime = DateTimeOffset.FromUnixTimeMilliseconds(record.Bt).UtcDateTime.AddHours(8),
                    sportProcess = new SportProcess(),
                    betItems = []
                };

                // 填充 ProcessInfo
                if (record.Mc != null) {
                    newMatch.sportProcess.mid = newMatch.gomid;
                    newMatch.sportProcess.sid = 1;
                    newMatch.sportProcess.type = _goMatchOptions.ProcessTypeMapping.TryGetValue(record.Mc.Pe.ToString(), out var type) ? (byte) type : (byte) 0;
                    newMatch.sportProcess.time = SecondsToMinutesSeconds(record.Mc.S);

                    var scoreItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 5 && n.Pe == 1000);
                    newMatch.sportProcess.ascore = scoreItem?.Sc?.ElementAtOrDefault(0) ?? 0;
                    newMatch.sportProcess.bscore = scoreItem?.Sc?.ElementAtOrDefault(1) ?? 0;

                    var redCardItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 8 && n.Pe == 1000);
                    var redScore = new ScoreItem(a: redCardItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: redCardItem?.Sc?.ElementAtOrDefault(1) ?? 0);
                    newMatch.sportProcess.ared = redScore.a;
                    newMatch.sportProcess.bred = redScore.b;

                    var yellowCardItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 7 && n.Pe == 1000);
                    var yellowCardScore = new ScoreItem(a: yellowCardItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: yellowCardItem?.Sc?.ElementAtOrDefault(1) ?? 0);

                    var cornerItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 6 && n.Pe == 1000);
                    var cornerScore = new ScoreItem(a: cornerItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: cornerItem?.Sc?.ElementAtOrDefault(1) ?? 0);

                    var extra = new Dictionary<string, ScoreItem>();
                    var scores = new Dictionary<string, ScoreItem>();

                    var h1ScoreItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 5 && n.Pe == 1002);
                    scores.Add("h1", new ScoreItem(a: h1ScoreItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: h1ScoreItem?.Sc?.ElementAtOrDefault(1) ?? 0));

                    var h2ScoreItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 5 && n.Pe == 1003);
                    scores.Add("h2", new ScoreItem(a: h2ScoreItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: h2ScoreItem?.Sc?.ElementAtOrDefault(1) ?? 0));

                    var ftScoreItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 5 && n.Pe == 1001);
                    scores.Add("ft", new ScoreItem(a: ftScoreItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: ftScoreItem?.Sc?.ElementAtOrDefault(1) ?? 0));

                    var etScoreItem = record.Nsg?.FirstOrDefault(n => n.Tyg == 5 && n.Pe == 1013);
                    var etScore = new ScoreItem(a: etScoreItem?.Sc?.ElementAtOrDefault(0) ?? 0, b: etScoreItem?.Sc?.ElementAtOrDefault(1) ?? 0);
                    

                    extra.AddRange(scores);
                    extra.Add("rc", redScore);
                    extra.Add("yc", yellowCardScore);
                    extra.Add("corner", cornerScore);
                    extra.Add("book", new ScoreItem(a: redScore.a * 2 + yellowCardScore.a, b: redScore.b * 2 + yellowCardScore.b));
                    extra.Add("et", etScore);

                    newMatch.sportProcess.extra = JsonSerializer.Serialize(extra);
                    newMatch.sportProcess.scores = JsonSerializer.Serialize(scores);
                }

                // 填充 BetItems
                if (record.Mg != null) {
                    foreach (var mgItem in record.Mg) {
                        if (mgItem.Mks != null) {
                            byte plate = 0;
                            foreach (var market in mgItem.Mks) {
                                var betItemConfigKey = $"{mgItem.Mty}{mgItem.Pe}";
                                if (_goMatchOptions.BetItemMapping.TryGetValue(betItemConfigKey, out var betConfig)) {
                                    var betItem = new BetItem {
                                        plate = plate,
                                        mid = newMatch.gomid,
                                        gid = betConfig.Gid,
                                        smode = (byte) betConfig.Smode,
                                        hot = betConfig.Hot,
                                        hcp = betConfig.Gid switch {
                                            1 or 101 or 201 => ConvertAsianHandicap(market.Li),
                                            3 or 103 or 203 => ConvertAsianOU(market.Li),
                                            _ => ""
                                        }
                                    };

                                    switch (betConfig.Smode) {
                                        case 2:
                                            betItem.odds1 = market.Ss == 1 ? (decimal) (market.Op?.ElementAtOrDefault(0)?.Od ?? 0) : 0;
                                            betItem.odds2 = market.Ss == 1 ? (decimal) (market.Op?.ElementAtOrDefault(1)?.Od ?? 0) : 0;
                                            betItem.odds3 = 0;
                                            break;
                                        case 3:
                                            betItem.odds1 = market.Ss == 1 ? (decimal) (market.Op?.ElementAtOrDefault(0)?.Od ?? 0) : 0;
                                            betItem.odds2 = market.Ss == 1 ? (decimal) (market.Op?.ElementAtOrDefault(2)?.Od ?? 0) : 0;
                                            betItem.odds3 = market.Ss == 1 ? (decimal) (market.Op?.ElementAtOrDefault(1)?.Od ?? 0) : 0;
                                            break;
                                        default:
                                            betItem.odds1 = 0;
                                            betItem.odds2 = 0;
                                            betItem.odds3 = 0;
                                            break;
                                    }
                                    newMatch.betItems.Add(betItem);
                                }
                                plate++;
                            }
                        }
                    }
                }
                newMatches.Add(newMatch);
            }
        });
        return newMatches;
    }

    private async Task SyncMatchesCache(Dictionary<int, MatchRecord> rawRecordsByMid)
    {
        var incomingMids = rawRecordsByMid.Keys.ToHashSet();

        // 1. 从缓存中识别过期的比赛并在数据库中更新其状态
        var cachedMidsToRemove = _fbMatches.Keys.Except(incomingMids).ToList();
        if (cachedMidsToRemove.Count != 0)
        {
            var goMidsToUpdateStatus = new List<int>();
            foreach (var midToRemove in cachedMidsToRemove)
            {
                if (_fbMatches.TryGetValue(midToRemove, out var expiredMatch) && expiredMatch.gomid > 0)
                {
                    goMidsToUpdateStatus.Add(expiredMatch.gomid);
                    _logger.LogInformation("比赛 {FbMid} (GoMid: {GoMid}) 不在传入数据中。标记为状态关闭。", expiredMatch.mid, expiredMatch.gomid);
                }
                _fbMatches.Remove(midToRemove);
            }

            if (goMidsToUpdateStatus.Any())
            {
                await _dbService.CloseGoMatches(goMidsToUpdateStatus);
                
                // 从缓存中移除这些已关闭的 gomid
                foreach (var gomid in goMidsToUpdateStatus)
                {
                    RemoveGoMidFromCache(gomid);
                }
            }
        }

        // 2. 从传入数据中识别新比赛（忽略正在异步处理的赛事）
        var newMids = incomingMids.Except(_fbMatches.Keys).Except(_pendingMids).ToList();
        if (newMids.Count != 0)
        {
            
            // 检查这些“新”比赛是否已存在于我们的数据库中
            var existingGoMids = await _dbService.GetExistingMatchGoMids(newMids);
            var trulyNewMatchesToCreate = new List<FbMatch>();

            foreach (var mid in newMids)
            {
                var record = rawRecordsByMid[mid];
                var newMatch = new FbMatch
                {
                    mid = mid,
                    Sid = 1,
                    betItems = [],
                    LnameDict = new Dictionary<string, string> { { "CMN", record.Lg.Na } },
                    AnameDict = new Dictionary<string, string> { { "CMN", record.Ts[0].Na } },
                    BnameDict = new Dictionary<string, string> { { "CMN", record.Ts[1].Na } },
                    llogo = record.Lg.Lurl,
                    alogo = record.Ts[0].Lurl,
                    blogo = record.Ts[1].Lurl,
                    mtime = DateTimeOffset.FromUnixTimeMilliseconds(record.Bt).UtcDateTime.AddHours(8)
                };

                if (existingGoMids.TryGetValue(mid, out var goMid))
                {
                    newMatch.gomid = goMid;
                    _fbMatches[mid] = newMatch; // 添加到缓存
                    _logger.LogDebug("为新的传入比赛 {FbMid} 找到现有 GoMid {GoMid}。", goMid, mid);
                }
                else
                {
                    // 这是一场真正的新比赛，需要完整的创建过程
                    trulyNewMatchesToCreate.Add(newMatch);
                }
            }

            // 3. 批量创建真正的新比赛（异步非阻塞处理）
            if (trulyNewMatchesToCreate.Count != 0)
            {
                _logger.LogInformation("发现 {Count} 场真正的新比赛需要处理和创建。", trulyNewMatchesToCreate.Count);
                // 异步处理新赛事创建，不阻塞主线程
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await ProcessNewMatchesInBulk(trulyNewMatchesToCreate);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "异步处理新比赛时发生错误");
                    }
                });
            }
            
        }
    }

    private async Task ProcessNewMatchesInBulk(List<FbMatch> newMatches)
    {
        // 设置pendingMids变量
        foreach (var match in newMatches)
        {
            _pendingMids.Add(match.mid);
        }

        // 步骤 1：有效地获取所有多语言数据
        await GetMultiLangNamesInBulk(newMatches);

        // 步骤 2：使用丰富的数据异步处理每个新比赛（非阻塞）
        foreach (var match in newMatches)
        {
            
            try
            {
                // 查询或创建联赛、球队
                match.golid = await _dbService.GetOrCreateLeague(match.LnameDict, match.llogo);
                match.goaid = await _dbService.GetOrCreateTeam(match.AnameDict, match.alogo);
                match.gobid = await _dbService.GetOrCreateTeam(match.BnameDict, match.blogo);

                // 创建比赛记录
                match.gomid = await _dbService.CreateGoMatch(match);
                await _dbService.CreateFBMatch(match);

                _logger.LogInformation("为 FB Mid: {FbMid} 创建了新的比赛记录，GoMid: {GoMid}", match.gomid, match.mid);

                // 处理完成后从pendingMids中移除
                _pendingMids.Remove(match.mid);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理新比赛 {FbMid} 时发生错误", match.mid);
                // 出错时也移除pending状态
                _pendingMids.Remove(match.mid);
            }
            
        }
    }

    private async Task GetMultiLangNamesInBulk(List<FbMatch> matches)
    {
        var matchIds = matches.Select(m => m.mid).ToHashSet();

        // 用于保存所有语言数据的结构：Dictionary<lang, Dictionary<matchId, MatchRecord>>
        var langDataCache = new Dictionary<string, Dictionary<long, MatchRecord>>();

        var languages = new[] { "ENG", "ZHO" };
        foreach (var lang in languages)
        {
            var res = await _fbCrawlerService.GetMatchListAsync(languageType: lang, leagueIds: _jobOptions.leagueIds);
            langDataCache[lang] = res.Where(r => matchIds.Contains((int)r.Id)).ToDictionary(r => r.Id, r => r);
        }

        // 用收集到的数据丰富每个比赛
        foreach (var match in matches)
        {
            foreach (var lang in languages)
            {
                if (langDataCache.TryGetValue(lang, out var langMatches) && langMatches.TryGetValue(match.mid, out var langMatchData))
                {
                    if (!match.LnameDict.TryAdd(lang, langMatchData.Lg.Na)) match.LnameDict[lang] = langMatchData.Lg.Na;
                    if (!match.AnameDict.TryAdd(lang, langMatchData.Ts[0].Na)) match.AnameDict[lang] = langMatchData.Ts[0].Na;
                    if (!match.BnameDict.TryAdd(lang, langMatchData.Ts[1].Na)) match.BnameDict[lang] = langMatchData.Ts[1].Na;
                }
            }
        }
    }

    public static string ConvertAsianHandicap(string value)
    {
        if (!decimal.TryParse(value, out decimal handicap))
            return value;

        string prefix = handicap > 0 ? "*" : "";
        decimal abs = Math.Abs(handicap);

        if (abs % 0.25m != 0)
            return value;

        int intPart = (int)Math.Floor(abs);
        decimal fraction = abs - intPart;

        string result = fraction switch
        {
            0.00m => $"{intPart}",
            0.25m => $"{intPart}/{intPart + 0.5m}",
            0.50m => $"{intPart + 0.5m}",
            0.75m => $"{intPart + 0.5m}/{intPart + 1}",
            _ => value
        };

        return prefix + result;
    }

    public static string ConvertAsianOU(string value)
    {
        if (!decimal.TryParse(value, out decimal handicap))
            return value;

        decimal abs = Math.Abs(handicap);

        if (abs % 0.25m != 0)
            return value;

        int intPart = (int)Math.Floor(abs);
        decimal fraction = abs - intPart;

        string result = fraction switch
        {
            0.00m => $"{intPart}",
            0.25m => $"{intPart}/{intPart + 0.5m}",
            0.50m => $"{intPart + 0.5m}",
            0.75m => $"{intPart + 0.5m}/{intPart + 1}",
            _ => value
        };

        return result;
    }

    public static string SecondsToMinutesSeconds(int totalSeconds)
    {
        int minutes = totalSeconds / 60;
        int seconds = totalSeconds % 60;
        return $"{minutes:D2}:{seconds:D2}";
    }

    /// <summary>
    /// 检查是否有漏掉的赛事
    /// </summary>
    private async Task CheckMissingMatches()
    {
        try
        {
            // 获取当前缓存中的赛事
            var currentGoMids = _fbMatches.Values.Where(m => m.gomid > 0).Select(m => m.gomid).ToHashSet();
            
            // 找出在 gomid 缓存中但不在当前缓存中的赛事（漏掉的赛事）
            var missingGoMids = _recentGoMids.Except(currentGoMids).ToList();
            
            if (missingGoMids.Count > 0)
            {
                _logger.LogWarning("发现 {Count} 个漏掉的赛事： {mids}", missingGoMids.Count, string.Join(",", missingGoMids));
                
                // 关闭这些漏掉的赛事
                await _dbService.CloseGoMatches(missingGoMids);
                
                // 从缓存中移除这些已关闭的 gomid
                foreach (var gomid in missingGoMids)
                {
                    RemoveGoMidFromCache(gomid);
                }
                
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查漏掉赛事时发生错误");
        }
    }

    /// <summary>
    /// 添加 gomid 到缓存
    /// </summary>
    private void AddGoMidToCache(int gomid)
    {
        if (gomid > 0)
        {
            _recentGoMids.Add(gomid);
        }
    }

    /// <summary>
    /// 从缓存中移除 gomid
    /// </summary>
    private void RemoveGoMidFromCache(int gomid)
    {
        if (gomid > 0)
        {
            _recentGoMids.Remove(gomid);
        }
    }

}
