using ASport.Abstraction.Extensions.Hosting;
using ASport.Job.FBCrawler.Models.GoSport;
using ASport.Job.FBCrawler.Options;
using System.Text.Json;

namespace ASport.Job.FBCrawler.Services;

public class ResultBackgroundService(ILogger<ResultBackgroundService> logger, JobOptions jobOptions, FBCrawlerService fBCrawler, DBService dBService, GoMatchOptions goMatchOptions) 
: TimingBackgroundService(logger, "Result", jobOptions.resultInterval) {

    protected override async Task OnTimerAsync(CancellationToken stoppingToken) {
        
        var matches = await dBService.GetNoResultMatches();
        if (matches.Count > 0) {
            // 按mtime同一天内的赛事分组，选出开始时间和结束时间
            var groupedMatches = matches.GroupBy(m => m.mtime.Date).ToList();

            // 循环分组，根据开始时间和结束时间+1秒，并转换成unix时间戳
            foreach (var group in groupedMatches) {
                var startTimestamp = ((DateTimeOffset) group.Key).ToUnixTimeMilliseconds();
                var endTimestamp = ((DateTimeOffset) group.Key.AddDays(1).AddSeconds(-1)).ToUnixTimeMilliseconds();
                
                
                
                // 请求赛果
                var results = await fBCrawler.GetResultListAsync(startTimestamp, endTimestamp);
                var canUpdateResultMids = await dBService.GetGoMatchNoResultMids(group.Select(x => x.gomid).ToList());
                // 根据返回的赛果List<MatchResultData>，匹配mid==id的赛果，存在，再判断nsg.Pe == 1001是否存在，存在则表示成功拿到赛果
                foreach (var match in group) {
                    
                    var result = results.FirstOrDefault(r => r.id == match.mid);
                    if (result != null && result.nsg != null && (result.nsg.Any(n => n.Pe == 1001) || DateTime.Now.Subtract(match.mtime).TotalHours > 2)) {
                        if (canUpdateResultMids.Contains(match.gomid)) {
                            foreach (var nsgItem in result.nsg) {
                                var key = $"{nsgItem.Tyg}_{nsgItem.Pe}";
                                if (goMatchOptions.ResultMapping.TryGetValue(key, out var resultConfig)) {
                                    var sportResult = new SportResultCrawl {
                                        mid = match.gomid,
                                        sid = match.Sid,
                                        rid = resultConfig.rid,
                                        rcode = resultConfig.rcode,
                                        a = nsgItem.Sc != null && nsgItem.Sc.Count > 0 ? nsgItem.Sc[0] : 0,
                                        b = nsgItem.Sc != null && nsgItem.Sc.Count > 1 ? nsgItem.Sc[1] : 0,
                                        type = resultConfig.type
                                    };
                                    await dBService.UpdateGoResult(sportResult);
                                    logger.LogInformation("更新Go赛果{mid}: {rid} {rcode} {a}-{b}",
                                        sportResult.mid, sportResult.rid, sportResult.rcode, sportResult.a, sportResult.b);
                                }
                            }
                            await dBService.UpdateGoMatchResultState(match.gomid);
                        } else {
                            logger.LogWarning("已填赛果 mid: {mid}", match.gomid);
                        }

                        var resultJson = JsonSerializer.Serialize(result.nsg);
                        await dBService.UpdateFBResult(match.mid, resultJson);
                        logger.LogInformation("更新FB赛果{mid}: {result}", match.mid, resultJson);
                    }
                }
            }
        }
    }

}
