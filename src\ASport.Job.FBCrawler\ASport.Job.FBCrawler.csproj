<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
    <LangVersion>default</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\ASport.Abstractions\ASport.Abstractions.csproj" />
    <ProjectReference Include="..\ASport.Lib.TelegramBot\ASport.Lib.TelegramBot.csproj" />
    <ProjectReference Include="..\Koo.Core\Koo.Core.csproj" />
    <ProjectReference Include="..\Koo.Redis\Koo.Redis.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7" />
  </ItemGroup>
</Project>
