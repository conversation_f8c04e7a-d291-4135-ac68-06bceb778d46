using ASport.Abstraction.Extensions.Hosting;
using ASport.Job.FBCrawler.Options;

namespace ASport.Job.FBCrawler.Services
{
    public class CrawlerBackgroundService(ILogger<CrawlerBackgroundService> logger, FBCrawlerService fbCrawlerService, DataProcessService dataProcessService, JobOptions jobOptions)
      : TimingBackgroundService(logger, "Crawler", jobOptions.crawlerInterval) {
        protected override async Task OnTimerAsync(CancellationToken stoppingToken)
        {
            
            var res = await fbCrawlerService.GetMatchListAsync(leagueIds: jobOptions.leagueIds);
            var fbMatches = await dataProcessService.ProcessRawResponse(res);
            
        }
    }
}
