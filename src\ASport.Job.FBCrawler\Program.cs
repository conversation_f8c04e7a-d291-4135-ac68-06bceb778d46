using ASport.Abstractions.Data;
using ASport.Abstractions.Redis;
using Koo.Core.Extensions;
using Koo.Logging.Serilog;
using Koo.Redis;
using Microsoft.AspNetCore.Hosting;

var host = Host.CreateDefaultBuilder(args)
  .Inject(services => {
    // init repository
    services.AddMultipleDb();
    services.AddMultipleRedis();
    services.AddAsyncInitialization();
    services.Configure<HostOptions>(options => options.ShutdownTimeout = TimeSpan.FromSeconds(10));
    services.AddWindowsService();
  })
  .UseSerilogDefault()
  .Build();

await host.InitAsync();
await host.RunAsync();