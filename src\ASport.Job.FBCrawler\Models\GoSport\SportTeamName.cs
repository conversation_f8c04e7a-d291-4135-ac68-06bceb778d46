
using Koo.Dapper.Extensions;

namespace ASport.Job.FBCrawler.Models.GoSport;


[Table("sport_team_name")]
public class SportTeamName {

    [ExplicitKey]
    public long id { get; set; } // tid * 100 + lang
    public byte sid { get; set; } // 球类ID
    public int tid { get; set; } // 球队ID
    public byte lang { get; set; } // 语言ID
    public string name { get; set; } // 球队名称
    public bool logo { get; set; } = true; // logo
    public DateTime updateAt { get; set; } = DateTime.Now; // 最后更新时间
    public string updateBy { get; set; } = "system"; // 最后更新账号
    public string origin_datasrc { get; set; } = "fb"; // 数据源 hg, bwin, lsp, user, fb, v9

}
