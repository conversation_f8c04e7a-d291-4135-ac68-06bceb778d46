

using Koo.Dapper.Extensions;

namespace ASport.Job.FBCrawler.Models.GoSport;

[Table("sport_league_name")]
public class SportLeagueName {
    [ExplicitKey]
    public long id { get; set; }

    public byte sid { get; set; }

    public int lid { get; set; }

    public byte lang { get; set; }

    public string name { get; set; }

    public bool logo { get; set; }

    public string origin_datasrc { get; set; } = "fb";
}
