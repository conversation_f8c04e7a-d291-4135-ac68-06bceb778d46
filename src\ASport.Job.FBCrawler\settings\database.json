﻿{
  "Databases": {
    "AdminDb": {
      "master": "Data Source = **************; Initial Catalog = ASport_Admin_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    },
    "MatchDb": {
      "master": "Data Source = **************; Initial Catalog = ASport_Match_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    },
    "UserDb": {
      "master": "Data Source = **************; Initial Catalog = ASport_User_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    },
    "LogDb": {
      "master": "Data Source = **************; Initial Catalog = ASport_Log_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    },
    "StatDb": {
      "master": "Data Source = **************; Initial Catalog = ASport_Stat_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    },
    "TopicDb": {
      "master": "Data Source = **************; Initial Catalog = ASport_Topic_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    },
    "SportsDataCenterDb": {
      "master": "Data Source = **************; Initial Catalog = SportsDataCenter; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
    }
  },
  "MultiRedis": {
    "MatchRedis": "*************:15559,password=ik8873kdh..!9393*,defaultDatabase=9,syncTimeout=6000",
    "UserRedis": "**************:6379,syncTimeout=60000",
    "OddsAdjustRedis": "**************:6379,syncTimeout=60000",
    "TradingRedis": "**************:6379,defaultDatabase=2",
    "MatchRedis2": "**************:6379,defaultDatabase=3"
  },
  "ConnectionStrings": {
    "LogDb": "Data Source = **************; Initial Catalog = ASport_Log_Test; User Id = ASportTest; Password = ********************; Pooling = True; Max Pool Size = 10000; Encrypt = True; MultipleActiveResultSets = True; TrustServerCertificate = True;"
  }
}
