
using Koo.Dapper.Extensions;

namespace ASport.Job.FBCrawler.Models.GoSport;

[Table("sport_team_info")]
public class SportTeamInfo {
    [ExplicitKey]
    public int id { get; set; }
    public string name { get; set; }
    public bool logo { get; set; }
    public byte state { get; set; } = 1;
    public string rlogo { get; set; }
    public byte down { get; set; } = 1;
    public string updateBy { get; set; } = "system";
    public DateTime updateAt { get; set; } = DateTime.Now;
}
