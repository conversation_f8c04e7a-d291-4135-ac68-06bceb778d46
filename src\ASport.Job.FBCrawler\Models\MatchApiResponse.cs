using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace ASport.Job.FBCrawler.Models
{
    public class MatchApiResponse<TData>
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("data")]
        public TData Data { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }
    }

    public class MatchListData
    {
        [JsonPropertyName("current")]
        public int Current { get; set; }

        [JsonPropertyName("size")]
        public int Size { get; set; }

        [JsonPropertyName("total")]
        public int Total { get; set; }

        [JsonPropertyName("records")]
        public List<MatchRecord> Records { get; set; }

        [JsonPropertyName("pageTotal")]
        public int PageTotal { get; set; }
    }

    public class MatchRecord
    {
        [JsonPropertyName("nsg")]
        public List<NsgItem> Nsg { get; set; }

        [JsonPropertyName("mg")]
        public List<MgItem> Mg { get; set; }

        [JsonPropertyName("tms")]
        public int Tms { get; set; }

        [JsonPropertyName("tps")]
        public List<string> Tps { get; set; }

        [JsonPropertyName("nm")]
        public string Nm { get; set; }

        [JsonPropertyName("lg")]
        public LeagueInfo Lg { get; set; }

        [JsonPropertyName("ts")]
        public List<TeamInfo> Ts { get; set; }

        [JsonPropertyName("mc")]
        public MatchConfig Mc { get; set; }

        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("bt")]
        public long Bt { get; set; }

        [JsonPropertyName("ms")]
        public int Ms { get; set; }

        [JsonPropertyName("fid")]
        public int Fid { get; set; }

        [JsonPropertyName("fmt")]
        public int Fmt { get; set; }

        [JsonPropertyName("ne")]
        public int Ne { get; set; }

        [JsonPropertyName("vs")]
        public VsInfo Vs { get; set; }

        [JsonPropertyName("sid")]
        public int Sid { get; set; }

        [JsonPropertyName("smt")]
        public int Smt { get; set; }

        [JsonPropertyName("ty")]
        public int Ty { get; set; }

        [JsonPropertyName("sb")]
        public object Sb { get; set; }

        [JsonPropertyName("pl")]
        public int Pl { get; set; }
    }

    public class NsgItem
    {
        [JsonPropertyName("pe")]
        public int Pe { get; set; }

        [JsonPropertyName("tyg")]
        public int Tyg { get; set; }

        [JsonPropertyName("sc")]
        public List<int> Sc { get; set; }
    }

    public class MgItem
    {
        [JsonPropertyName("mty")]
        public int Mty { get; set; }

        [JsonPropertyName("pe")]
        public int Pe { get; set; }

        [JsonPropertyName("mks")]
        public List<Market> Mks { get; set; }

        [JsonPropertyName("tps")]
        public List<string> Tps { get; set; }

        [JsonPropertyName("nm")]
        public string Nm { get; set; }
    }

    public class Market
    {
        [JsonPropertyName("op")]
        public List<Option> Op { get; set; }

        [JsonPropertyName("id")]
        public long Id { get; set; }

        [JsonPropertyName("ss")]
        public int Ss { get; set; } // 0-锁盘，1-开放

        [JsonPropertyName("au")]
        public int Au { get; set; }

        [JsonPropertyName("mbl")]
        public int Mbl { get; set; }

        [JsonPropertyName("li")]
        public string Li { get; set; }
    }

    public class Option
    {
        [JsonPropertyName("na")]
        public string Na { get; set; }

        [JsonPropertyName("nm")]
        public string Nm { get; set; }

        [JsonPropertyName("ty")]
        public int Ty { get; set; }

        [JsonPropertyName("od")]
        public double Od { get; set; }

        [JsonPropertyName("bod")]
        public double Bod { get; set; }

        [JsonPropertyName("odt")]
        public int Odt { get; set; }

        [JsonPropertyName("li")]
        public string Li { get; set; }
    }

    public class LeagueInfo
    {
        [JsonPropertyName("na")]
        public string Na { get; set; }

        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("or")]
        public int Or { get; set; }

        [JsonPropertyName("lurl")]
        public string Lurl { get; set; }

        [JsonPropertyName("sid")]
        public int Sid { get; set; }

        [JsonPropertyName("rid")]
        public int Rid { get; set; }

        [JsonPropertyName("rnm")]
        public string Rnm { get; set; }

        [JsonPropertyName("rlg")]
        public string Rlg { get; set; }

        [JsonPropertyName("hot")]
        public bool Hot { get; set; }

        [JsonPropertyName("slid")]
        public int Slid { get; set; }
    }

    public class TeamInfo
    {
        [JsonPropertyName("na")]
        public string Na { get; set; }

        [JsonPropertyName("id")]
        public int Id { get; set; }

        [JsonPropertyName("lurl")]
        public string Lurl { get; set; }
    }

    public class MatchConfig
    {
        [JsonPropertyName("s")]
        public int S { get; set; }

        [JsonPropertyName("pe")]
        public int Pe { get; set; }

        [JsonPropertyName("r")]
        public bool R { get; set; }

        [JsonPropertyName("tp")]
        public int Tp { get; set; }
    }

    public class VsInfo
    {
        [JsonPropertyName("have")]
        public bool Have { get; set; }
    }
}