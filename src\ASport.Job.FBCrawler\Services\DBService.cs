using ASport.Abstraction.Data;
using ASport.Abstractions.Redis;
using ASport.Job.FBCrawler.Models;
using ASport.Job.FBCrawler.Models.GoSport;
using ASport.Job.FBCrawler.Options;
using Dapper;
using Koo.Dapper;
using Koo.Data;
using Koo.DependencyInjection;
using Koo.Redis;

namespace ASport.Job.FBCrawler.Services;

public class DBService(ILogger<DBService> logger, IDb<MatchDb> matchDb, IDb<LogDb> logDb, GoMatchOptions goMatchOptions, IRedis<MatchRedis2> redis) : ISingleton {

    public async Task<Dictionary<int, int>> GetExistingMatchGoMids(List<int> fbMids) {
        var fbMatches = await logDb.UseSlavePriority().FetchAsync<FbMatch>("select mid, gomid from fb_matches with(nolock) where mid in @mids", new { mids = fbMids });
        return fbMatches.ToDictionary(k => k.mid, v => v.gomid);
    }

    public async Task CloseGoMatches(List<int> goMids) {
        for (var i = 0; i < 3; i++) {
            try {
                await matchDb.ExecuteAsync("update sport_match set state = 0, updateAt = GETDATE(), atime = GETDATE() where mid in @mids and copen = 1 and state = 1", new { mids = goMids });
                return;
            } catch (Exception e) {

                logger.LogError("close go matches error [{i}]: {msg}", i + 1, e.Message);
            }
        }
    }

    public async Task<int> GetOrCreateLeague(Dictionary<string, string> names, string logoUrl) {

        var engName = names.TryGetValue("ENG", out var name) ? name : names.FirstOrDefault().Value;

        var lids = await matchDb.FetchAsync<int>("select lid from sport_league_name with(nolock) where name = @name", new { name = engName });
        var lid = lids.Where(x => x % 100 == 88).FirstOrDefault();
        if (lid > 0) {
            return lid;
        }

        var newid = await matchDb.FirstOrDefaultAsync<int>("select NEXT VALUE FOR seq_league_88");
        lid = newid * 100 + 88;
        var leagues = new List<SportLeagueName>();
        foreach (var item in names) {
            if (goMatchOptions.LangMapping.TryGetValue(item.Key, out var langId)) {
                leagues.Add(new SportLeagueName {
                    id = lid * 100 + langId,
                    lid = lid,
                    sid = 1,
                    lang = langId,
                    logo = true,
                    name = item.Value
                });
            }
        }
        if (leagues.Count > 0) {
            await matchDb.InsertAsync(leagues);
            await matchDb.InsertAsync(new SportLeagueInfo {
                id = lid,
                name = engName,
                rlogo = logoUrl,
                down = 1
            });
        }

        logger.LogInformation("Created new league: {LeagueName} with ID: {LeagueId}", engName, lid);
        return lid;
    }

    public async Task<int> GetOrCreateTeam(Dictionary<string, string> names, string logoUrl) {
        var engName = names.TryGetValue("ENG", out var name) ? name : names.FirstOrDefault().Value;

        var tids = await matchDb.FetchAsync<int>("select tid from sport_team_name with(nolock) where name = @name", new { name = engName });
        var tid = tids.Where(x => x % 100 == 88).FirstOrDefault();
        if (tid > 0) {
            return tid;
        }

        var newid = await matchDb.FirstOrDefaultAsync<int>("select NEXT VALUE FOR seq_team_88");
        tid = newid * 100 + 88;
        var teams = new List<SportTeamName>();
        foreach (var item in names) {
            if (goMatchOptions.LangMapping.TryGetValue(item.Key, out var langId)) {
                teams.Add(new SportTeamName {
                    id = tid * 100 + langId,
                    tid = tid,
                    sid = 1,
                    lang = langId,
                    logo = true,
                    name = item.Value
                });
            }
        }
        if (teams.Count > 0) {
            await matchDb.InsertAsync(teams);
            await matchDb.InsertAsync(new SportTeamInfo {
                id = tid,
                name = engName,
                rlogo = logoUrl,
                down = 1
            });
        }
        logger.LogInformation("Created new team: {TeamName}  with ID: {TeamId}", engName, tid);
        return tid;
    }

    public async Task<int> CreateGoMatch(FbMatch match) {
        var mid = await matchDb.FirstOrDefaultAsync<int>("select mid from sport_match with(nolock) where lid = @lid and aid = @aid and bid = @bid and mtime = @mtime",
        new {
            lid = match.golid,
            aid = match.goaid,
            bid = match.gobid,
            mtime = match.mtime
        });
        if (mid > 988_000_000) {
            return mid;
        }

        var newid = await matchDb.FirstOrDefaultAsync<int>("select NEXT VALUE FOR seq_match_88");
        mid = newid + 988_000_000;
        var newMatch = new SportMatch() {
            mid = mid,
            sid = 1,
            lid = match.golid,
            aid = match.goaid,
            bid = match.gobid,
            mt = 3,
            parlay = true,
            running = true,
            state = 1,
            copen = true,
            cupdate = true,
            mtime = match.mtime
        };
        await matchDb.InsertAsync(newMatch);
        match.sportProcess.mid = mid;
        logger.LogInformation("Created new match: {mid}", mid);
        return mid;
    }

    public async Task CreateFBMatch(FbMatch match) {
        await logDb.InsertAsync(match);
    }

    public async Task BulkUpsertSportProcess(List<SportProcess> data) {
        await matchDb.SpExecuteAsync("usp_sport_process_bulk_upsert",
        new { data = data.ToDataTable().AsTableValuedParameter("TT_SPORT_PROCESS_BULK_UPSERT_DATA") });
    }

    // 获取go赛事状态
    public async Task<List<SportMatch>> GetSportMatchInfo(List<int> mids) {
        var matches = await matchDb.FetchAsync<SportMatch>("select mid, lid, aid, bid, mt, parlay, running, state, copen, cupdate, mtime from sport_match with(nolock) where mid in @mids", new { mids });
        return matches;
    }


    // 批量更新赛事数据，并返回cupdate=0的mid
    public async Task<List<int>> BulkUpdateSportMatch(List<UpdateSportMatchData> data) {
        if (data.Count == 0) return [];
        var res = await matchDb.SpExecuteAsync("usp_sport_match_bulk_update", new { data = data.ToDataTable().AsTableValuedParameter("TT_SPORT_MATCH_BULK_UPDATE_DATA") });
        if (res.code == 200) {
            if (!string.IsNullOrEmpty(res.message)) {
                return [.. res.message.Split(",").Select(int.Parse)];
            }
            return [];
        }
        throw new Exception(res.message);

    }

    // 批量更新redis hash 
    public async Task BulkUpdateRedis(string key, Dictionary<string, object> data) {
        await redis.HMSetAsync(key, data);
    }

    // 获取所有redis hash
    public async Task<Dictionary<string, T>> GetRedisHashAll<T>(string key) where T : class {
        return await redis.HGetAllAsync<T>(key);
    }

    // 获取需要清理的活跃Go赛事ID
    public async Task<List<int>> GetActiveGoMatchesForCleanup() {
        return await matchDb.FetchAsync<int>("SELECT mid FROM sport_match WITH(NOLOCK) WHERE mid > 988000000 AND sid = 1 AND copen = 1 AND state = 1");
    }

    // 获取未有赛果的赛事
    public async Task<List<FbMatch>> GetNoResultMatches() {
        return await logDb.FetchAsync<FbMatch>(@"select mid, gomid, sid, mtime from fb_matches with(nolock) 
        where syncResult = 0 and mtime < DATEADD(MINUTE, -80, GETDATE()) and mtime > DATEADD(MINUTE, -360, GETDATE())  order by mtime");
    }

    // 赛果保存
    public async Task UpdateFBResult(int mid, string result) {
        await logDb.ExecuteAsync("update fb_matches set syncResult =1, result = @result, updateAt = GETDATE() where mid = @mid", new { mid, result });
    }

    // 更新或插入Go赛事结果
    public async Task UpdateGoResult(SportResultCrawl result) {
        // 使用MERGE语句实现存在则更新，不存在则插入
        var sql = @"
            MERGE INTO sport_result_crawl WITH (HOLDLOCK) AS target
            USING (SELECT @mid AS mid, @sid AS sid, @rid AS rid, @rcode AS rcode, @hcp AS hcp,
                          @a AS a, @b AS b, @c AS c, @type AS type, @state AS state) AS source
            ON target.mid = source.mid AND target.rid = source.rid AND target.hcp = source.hcp
            WHEN MATCHED THEN
                UPDATE SET
                    target.sid = source.sid,
                    target.rcode = source.rcode,
                    target.a = source.a,
                    target.b = source.b,
                    target.c = source.c,
                    target.type = source.type,
                    target.state = source.state,
                    target.updateAt = GETDATE()
            WHEN NOT MATCHED THEN
                INSERT (uk, mid, sid, rid, rcode, hcp, a, b, c, type, state, createAt, updateAt)
                VALUES (@uk, @mid, @sid, @rid, @rcode, @hcp, @a, @b, @c, @type, @state, GETDATE(), GETDATE());";

        await matchDb.ExecuteAsync(sql, new {
            result.uk,
            result.mid,
            result.sid,
            result.rid,
            result.rcode,
            result.hcp,
            result.a,
            result.b,
            result.c,
            result.type,
            result.state
        });
    }

    // 更新go体育赛果状态
    public async Task UpdateGoMatchResultState(int mid) {
        await matchDb.ExecuteAsync("update sport_match set rstate = 1, updateAt = GETDATE() where mid = @mid", new { mid });
    }

    // 获取go体育未填赛果的mid
    public async Task<List<int>> GetGoMatchNoResultMids(List<int> mids) {
        return await matchDb.FetchAsync<int>("select mid from sport_match with(nolock) where mid in @mids and rstate = 0", new { mids });
    }
}