using System.ComponentModel.DataAnnotations.Schema;

namespace ASport.Job.FBCrawler.Models.GoSport;

[Table("sport_result_crawl")]
public class SportResultCrawl {
    public int id { get; set; }
    public string uk => $"{mid},{rid},{hcp}";
    public int mid { get; set; }
    public byte sid { get; set; }
    public int rid { get; set; }
    public string rcode { get; set; }
    public int hcp { get; set; } = 0;
    public int a { get; set; } = 0;
    public int b { get; set; } = 0;
    public string c { get; set; } = "";
    public byte type { get; set; }
    public byte state { get; set; } = 1;
    public DateTime createAt { get; set; } = DateTime.Now;
    public DateTime updateAt { get; set; } = DateTime.Now;
}