
using System.ComponentModel.DataAnnotations.Schema;

namespace ASport.Job.FBCrawler.Models;

[Table("sport_match")]
public class SportMatch {
    public int mid { get; set; }
    public byte sid { get; set; }
    public int lid { get; set; }
    public int aid { get; set; }
    public int bid { get; set; }
    public byte mt { get; set; }

    public DateTime mtime { get; set; }
    public bool parlay { get; set; }
    public int gcount { get; set; }
    public bool running { get; set; }

    public byte state { get; set; }

    public bool copen { get; set; }
    public bool cupdate { get; set; }

    public DateTime updateAt { get; set; } = DateTime.Now;

}
