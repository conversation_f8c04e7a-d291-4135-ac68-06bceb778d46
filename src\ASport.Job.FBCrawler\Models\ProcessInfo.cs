using System.ComponentModel.DataAnnotations.Schema;

namespace ASport.Job.FBCrawler.Models;


[Table("sport_process")]
public class SportProcess {

    public int mid { get; set; }

    public byte sid { get; set; }

    public byte type { get; set; }

    public string time { get; set; }

    public int ascore { get; set; }

    public int bscore { get; set; }

    public int ared { get; set; }

    public int bred { get; set; }

    public string extra { get; set; } = "";

    public string scores { get; set; } = "";

    public int duration { get; set; } = 85;

    public DateTime? scoreAt { get; set; } = DateTime.Now;

    public int version { get; set; } = 1;

}


public record ScoreItem(int a, int b);
