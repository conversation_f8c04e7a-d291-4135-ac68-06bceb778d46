
namespace ASport.Job.FBCrawler.Models
{
    public class BetItem {
        public long id => mid * 1_000_000L + mt * 1_00_000L + gid * 1_00L + plate;

        public int mid { get; set; }

        public byte mt { get; set; } = 3;

        public byte sid { get; set; } = 1;

        public int gid { get; set; }

        public byte smode { get; set; }

        public string hcp { get; set; }

        public string score { get; set; }

        public byte plate { get; set; }

        public decimal odds1 { get; set; }

        public decimal odds2 { get; set; }

        public decimal odds3 { get; set; } = 0;

        public string odds { get; set; }

        public int version { get; set; } = 1;

        public byte hot { get; set; }

        public byte state { get; set; } = 1;

        public DateTime createAt { get; set; } = DateTime.Now;

        public DateTime updateAt { get; set; } = DateTime.Now;
        
    }
}